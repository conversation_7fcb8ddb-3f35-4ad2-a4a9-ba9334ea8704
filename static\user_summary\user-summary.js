/**
 * 用户数据汇总模块
 */
// 确保layui已加载
if (typeof layui !== 'undefined') {
  // 用户数据汇总模块定义
  window.UserSummary = {
    // 初始化配置选项
    options: {
      tableId: 'LAY-user-data-summary',
      dateRangeId: 'date-range',
      apiUrl: '/api/table/user',
      defaultDateRange: {
        earliest_date: null,
        latest_date: null
      }
    },
    
    // 初始化用户数据汇总模块
    init: function() {
      var that = this;
      var $ = layui.$;
      var admin = layui.admin;
      var table = layui.table;
      var form = layui.form;
      var laydate = layui.laydate;
      
      // 加载默认日期范围
      this.loadDefaultDateRange();
      
      // 初始化日期选择器
      this.initDatePicker();
      
      // 初始化表格
      this.initTable();
      
      // 初始化事件
      this.initEvents();
    },
    
    // 加载默认日期范围
    loadDefaultDateRange: function() {
      var that = this;
      var $ = layui.$;
      
      // 从后端获取默认日期范围
      $.ajax({
        url: '/api/date-range/default',
        type: 'GET',
        dataType: 'json',
        async: false, // 同步请求，确保在初始化日期选择器前获取到数据
        success: function(res) {
          if(res.code === 0 && res.data) {
            that.options.defaultDateRange = res.data;
            console.log('获取到默认日期范围:', that.options.defaultDateRange);
          }
        },
        error: function(err) {
          console.error('获取默认日期范围出错:', err);
        }
      });
    },
    
    // 初始化日期选择器
    initDatePicker: function() {
      var that = this;
      var laydate = layui.laydate;
      var $ = layui.$;
      
      // 日期选择器
      // 获取上个月的第一天和最后一天
      var date = new Date();
      var year = date.getFullYear();
      var month = date.getMonth();
      var firstDayLastMonth = new Date(year, month - 1, 1);
      var lastDayLastMonth = new Date(year, month, 0);
      
      // 格式化日期为 YYYY-MM-DD
      var formatDate = function(date) {
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        var d = date.getDate();
        return y + '-' + (m < 10 ? '0' + m : m) + '-' + (d < 10 ? '0' + d : d);
      };
      
      var firstDayStr = formatDate(firstDayLastMonth);
      var lastDayStr = formatDate(lastDayLastMonth);
      
      laydate.render({
        elem: '#' + this.options.dateRangeId,
        range: true,
        type: 'datetime', // 设置为日期时间选择
        value: firstDayStr + ' 00:00:00 - ' + lastDayStr + ' 23:59:59',
        shortcuts: [
          {
            text: "上个月",
            value: function(){
              var date = new Date();
              var year = date.getFullYear();
              var month = date.getMonth();
              return [
                new Date(year, month - 1, 1, 0, 0, 0),
                new Date(year, month, 0, 23, 59, 59)
              ];
            }
          },
          {
            text: "本月",
            value: function(){
              var date = new Date();
              var year = date.getFullYear();
              var month = date.getMonth();
              return [
                new Date(year, month, 1, 0, 0, 0),
                new Date(year, month + 1, 0, 23, 59, 59)
              ];
            }
          },
          {
            text: "本年",
            value: function(){
              var date = new Date();
              var year = date.getFullYear();
              return [
                new Date(year, 0, 1, 0, 0, 0),
                new Date(year, 11, 31, 23, 59, 59)
              ];
            }
          },
          {
            text: "去年",
            value: function(){
              var date = new Date();
              var year = date.getFullYear() - 1;
              return [
                new Date(year, 0, 1, 0, 0, 0),
                new Date(year, 11, 31, 23, 59, 59)
              ];
            }
          },
          {
            text: "最近一周",
            value: function(){
              var date = new Date();
              var lastWeek = new Date(date.getTime() - 7 * 24 * 60 * 60 * 1000);
              return [
                new Date(lastWeek.getFullYear(), lastWeek.getMonth(), lastWeek.getDate(), 0, 0, 0),
                new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)
              ];
            }
          },
          {
            text: "最近一月",
            value: function(){
              var date = new Date();
              var lastMonth = new Date(date.getTime() - 30 * 24 * 60 * 60 * 1000);
              return [
                new Date(lastMonth.getFullYear(), lastMonth.getMonth(), lastMonth.getDate(), 0, 0, 0),
                new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)
              ];
            }
          },
          {
            text: "最近三月",
            value: function(){
              var date = new Date();
              var lastThreeMonths = new Date(date.getTime() - 90 * 24 * 60 * 60 * 1000);
              return [
                new Date(lastThreeMonths.getFullYear(), lastThreeMonths.getMonth(), lastThreeMonths.getDate(), 0, 0, 0),
                new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)
              ];
            }
          },
          {
            text: "最近半年",
            value: function(){
              var date = new Date();
              var lastSixMonths = new Date(date.getFullYear(), date.getMonth() - 6, date.getDate());
              return [
                new Date(lastSixMonths.getFullYear(), lastSixMonths.getMonth(), lastSixMonths.getDate(), 0, 0, 0),
                new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)
              ];
            }
          }
        ]
      });
    },
    
    // 初始化表格
    initTable: function() {
      var that = this;
      var table = layui.table;
      
      // 渲染表格
      table.render({
        elem: '#' + this.options.tableId
        ,url: this.options.apiUrl
        ,page: true
        ,limit: 20
        ,limits: [10, 20, 50, 100, 200]
        ,skin: 'line row' // 添加行边框风格
        ,even: true // 开启隔行背景
        ,where: this.options.defaultDateRange.earliest_date && this.options.defaultDateRange.latest_date ? {
          'date-range': this.options.defaultDateRange.earliest_date + ' 00:00:00 - ' + this.options.defaultDateRange.latest_date + ' 23:59:59'
        } : {}
        ,cols: [[
          {type: 'numbers', fixed: 'left', title: '序号', width: 60}
          ,{field: 'username', title: '用户名', width: 120, sort: true, fixed: 'left'}
          ,{field: 'classify', title: '客户名称', width: 240, sort: true, fixed: 'left'}
          ,{field: 'obs_capacity', title: 'OBS容量', width: 110, sort: true, align: 'right', templet: function(d){
            // 直接返回后端已经格式化好的OBS容量
            return d.obs_capacity || '0.00GB';
          }, sort: function(a, b) {
            // 使用数值进行排序
            var valA = a.obs_capacity_value || 0;
            var valB = b.obs_capacity_value || 0;
            return valA - valB;
          }}
          ,{field: 'experience', title: '累计作业数', width: 120, sort: true, align: 'right'}
          ,{field: 'card_time', title: '共享卡时', width: 110, sort: true, align: 'right', templet: function(d){
            var value = d.card_time || 0;
            return (typeof value === 'number') ? value.toLocaleString() : value;
          }}
          ,{field: 'exclusive_card_time', title: '专属卡时', width: 110, sort: true, align: 'right', templet: function(d){
            var value = d.exclusive_card_time || 0;
            return (typeof value === 'number') ? value.toLocaleString() : value;
          }}
          ,{field: 'exclusive_card_time_adjusted', title: '专属卡时(无误差)', width: 160, sort: true, align: 'right', templet: function(d){
            var value = d.exclusive_card_time_adjusted || 0;
            var originalValue = d.exclusive_card_time || 0;
            var isDifferent = Math.abs(value - originalValue) > 0.01; // 使用小的阈值来处理浮点数比较
            var cls = isDifferent ? 'money-different' : '';
            return '<span class="'+ cls +'">'+ ((typeof value === 'number') ? value.toLocaleString() : value) +'</span>';
          }}
          ,{field: 'wealth', title: '总支出金额', width: 120, sort: true, align: 'right', templet: function(d){
            var value = d.wealth || 0;
            return (typeof value === 'number') ? value.toLocaleString() : value;
          }}
          ,{field: 'income', title: '总收入金额', width: 120, sort: true, align: 'right', templet: function(d){
            var value = d.income || 0;
            return (typeof value === 'number') ? value.toLocaleString() : value;
          }}
          ,{field: 'net_income', title: '净收支', width: 120, sort: true, align: 'right', templet: function(d){
            var net = d.net_income || 0;
            var cls = net >= 0 ? 'money-positive' : 'money-negative';
            return '<span class="'+ cls +'">'+ ((typeof net === 'number') ? net.toLocaleString() : net) +'</span>';
          }}
          ,{field: 'current_balance', title: '当前现金余额', width: 130, sort: true, align: 'right', templet: function(d){
            var value = d.current_balance || 0;
            var cls = value >= 0 ? 'money-positive' : 'money-negative';
            return '<span class="'+ cls +'">'+ ((typeof value === 'number') ? value.toLocaleString() : value) +'</span>';
          }}
          ,{field: 'credit_limit', title: '当前信用额度', width: 130, sort: true, align: 'right', templet: function(d){
            var value = d.credit_limit || 0;
            return (typeof value === 'number') ? value.toLocaleString() : value;
          }}
          ,{field: 'transactions', title: '交易总次数', width: 110, sort: true, align: 'right', templet: function(d){
            var value = d.transactions || 0;
            return (typeof value === 'number') ? value.toLocaleString() : value;
          }}
          ,{field: 'outgoing_transactions', title: '支出次数', width: 110, sort: true, align: 'right', templet: function(d){
            var value = d.outgoing_transactions || 0;
            return (typeof value === 'number') ? value.toLocaleString() : value;
          }}
          ,{field: 'incoming_transactions', title: '收入次数', width: 110, sort: true, align: 'right', templet: function(d){
            var value = d.incoming_transactions || 0;
            return (typeof value === 'number') ? value.toLocaleString() : value;
          }}
          ,{field: 'earliest_transaction', title: '最早交易时间', width: 160, sort: true}
          ,{field: 'latest_transaction', title: '最晚交易时间', width: 160, sort: true}
          ,{title: '操作', width: 150, align:'center', fixed: 'right', toolbar: '#table-operation'}
        ]]
        ,height: 'full-220'
        ,text: {
          none: '暂无相关数据'
        }
        ,parseData: function(res){
          return {
            "code": res.code,
            "msg": res.msg,
            "count": res.count,
            "data": res.data
          };
        }
      });
      
      // 监听表格工具条
      var $ = layui.$;
      var layer = layui.layer;
      table.on('tool(' + this.options.tableId + ')', function(obj){
        var data = obj.data;
        if(obj.event === 'check'){
          // 查看作业 - 使用全局UserJobs对象
          if (window.UserJobs) {
            window.UserJobs.openJobsDialog(data);
          } else {
            layer.msg('作业模块加载失败', {icon: 2});
          }
        } else if(obj.event === 'usage'){
          // 查看费用 - 使用全局UserDetail对象
          if (window.UserDetail) {
            window.UserDetail.openDetailDialog(data);
          } else {
            layer.msg('费用模块加载失败', {icon: 2});
          }
        }
      });
    },
    
    // 防抖函数
    debounce: function(func, wait) {
      var timeout;
      return function() {
        var context = this, args = arguments;
        var later = function() {
          timeout = null;
          func.apply(context, args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },
    
    // 初始化事件监听
    initEvents: function() {
      var that = this;
      var $ = layui.$;
      var table = layui.table;
      var form = layui.form;
      var layer = layui.layer;
      
      // 重置按钮
      $('#btn-reset').on('click', this.debounce(function(){
        // 清空输入框
        $('input[name="username"]').val('');
        
        // 显示加载中状态
        var loadingIndex = layer.msg('正在重置数据...', {
          icon: 16,
          shade: 0.01,
          time: 0 // 不自动关闭
        });
        
        // 重置日期范围为默认值
        if(that.options.defaultDateRange.earliest_date && that.options.defaultDateRange.latest_date) {
          var defaultDateStr = that.options.defaultDateRange.earliest_date + ' 00:00:00 - ' + that.options.defaultDateRange.latest_date + ' 23:59:59';
          $('#' + that.options.dateRangeId).val(defaultDateStr);
          
          // 重新加载表格，使用默认日期范围
          table.reload(that.options.tableId, {
            where: {
              'date-range': defaultDateStr
            },
            done: function(){
              // 数据加载完成后关闭加载提示
              layer.close(loadingIndex);
            }
          });
        } else {
          // 如果没有默认日期范围，则清空日期输入框
          $('#' + that.options.dateRangeId).val('');
          
          // 重新加载表格，不带任何查询条件
          table.reload(that.options.tableId, {
            where: {}, // 清空所有查询条件
            done: function(){
              // 数据加载完成后关闭加载提示
              layer.close(loadingIndex);
            }
          });
        }
      }, 500)); // 500毫秒的防抖时间
      
      // 刷新数据
      $('#btn-refresh').on('click', this.debounce(function(){
        // 显示加载中状态
        var loadingIndex = layer.msg('正在刷新数据...', {
          icon: 16,
          shade: 0.01,
          time: 0 // 不自动关闭
        });
        
        // 重新加载表格
        table.reload(that.options.tableId, {
          where: table.getOptions(that.options.tableId).where || {},
          done: function(){
            // 数据加载完成后关闭加载提示
            layer.close(loadingIndex);
          }
        });
      }, 500)); // 500毫秒的防抖时间
      
      // 监听搜索
      form.on('submit(LAY-user-front-search)', this.debounce(function(data){
        var field = data.field;
        
        // 显示加载中状态
        var loadingIndex = layer.msg('正在搜索数据...', {
          icon: 16,
          shade: 0.01,
          time: 0 // 不自动关闭
        });
        
        // 执行重载
        table.reload(that.options.tableId, {
          where: field,
          done: function(){
            // 数据加载完成后关闭加载提示
            layer.close(loadingIndex);
          }
        });
        
        return false;
      }, 500)); // 500毫秒的防抖时间
      
      // 导出按钮
      $('#btn-export').on('click', this.initExportDialog.bind(this));
    },
    
    // 初始化导出对话框
    initExportDialog: function() {
      var that = this;
      var $ = layui.$;
      var layer = layui.layer;
      var form = layui.form;
      var laydate = layui.laydate;
      var table = layui.table;
      
      // 显示导出对话框
      layer.open({
        type: 1,
        title: '导出数据',
        area: ['500px', '350px'],
        content: `
          <div class="layui-card">
            <div class="layui-card-body">
              <form class="layui-form" id="export-form" lay-filter="export-form">
                <div class="layui-form-item">
                  <label class="layui-form-label">导出选项</label>
                  <div class="layui-input-block">
                    <input type="radio" name="export-mode" value="current" title="导出当前汇总列表" checked lay-filter="export-mode">
                    <input type="radio" name="export-mode" value="single" title="导出单月汇总" lay-filter="export-mode">
                    <input type="radio" name="export-mode" value="multi" title="从开始月份导出到结束月份" lay-filter="export-mode">
                  </div>
                </div>
                
                <!-- 单月导出选项 -->
                <div class="layui-form-item" id="single-month-option" style="display: none;">
                  <div class="layui-inline">
                    <label class="layui-form-label">选择月份</label>
                    <div class="layui-input-inline">
                      <input type="text" class="layui-input" id="single-month-date" placeholder="yyyy-MM">
                    </div>
                  </div>
                  <div class="layui-form-mid layui-word-aux">选择月份后将导出该月从第一天到最后一天的数据</div>
                </div>
                
                <!-- 多月导出选项 -->
                <div class="layui-form-item" id="multi-month-option" style="display: none;">
                  <div class="layui-inline">
                    <label class="layui-form-label">月份范围</label>
                    <div class="layui-input-inline" style="width: 250px;">
                      <input type="text" class="layui-input" id="month-range-date" placeholder="开始月份 ~ 结束月份">
                    </div>
                  </div>
                  <div class="layui-form-mid layui-word-aux">将导出从开始月份到结束月份的所有数据</div>
                </div>
                
                <div class="layui-form-item">
                  <label class="layui-form-label">导出格式</label>
                  <div class="layui-input-block">
                    <input type="radio" name="export-format" value="xlsx" title="Excel (xlsx)" checked>
                    <input type="radio" name="export-format" value="csv" title="CSV">
                  </div>
                </div>
                
                <div class="layui-form-item">
                  <div class="layui-input-block">
                    <button type="button" class="layui-btn" id="btn-do-export">导出</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        `,
        success: function(layero, index) {
          // 渲染表单
          form.render(null, 'export-form');
          
          // 获取最早和最晚日期
          var earliestDate = null;
          var latestDate = null;
          
          // 从后端获取日期范围
          $.ajax({
            url: '/api/date-range/default',
            type: 'GET',
            dataType: 'json',
            async: false,
            success: function(res) {
              if(res.code === 0 && res.data) {
                earliestDate = res.data.earliest_date;
                latestDate = res.data.latest_date;
              }
            }
          });
          
          // 初始化月份选择器
          laydate.render({
            elem: '#single-month-date',
            type: 'month',
            value: latestDate ? latestDate.substring(0, 7) : ''
          });
          
          laydate.render({
            elem: '#month-range-date',
            type: 'month',
            range: '~'
          });
          
          // 监听单选按钮切换
          form.on('radio(export-mode)', function(data){
            var mode = data.value;
            if(mode === 'current') {
              $('#single-month-option').hide();
              $('#multi-month-option').hide();
            } else if(mode === 'single') {
              $('#single-month-option').show();
              $('#multi-month-option').hide();
            } else if(mode === 'multi') {
              $('#single-month-option').hide();
              $('#multi-month-option').show();
            }
          });
          
          // 导出按钮点击事件
          $('#btn-do-export').on('click', function() {
            // 获取表单数据
            var exportMode = $('input[name="export-mode"]:checked').val();
            var exportFormat = $('input[name="export-format"]:checked').val();
            
            // 获取当前查询条件
            var queryParams = table.getOptions(that.options.tableId).where || {};
            
            // 根据导出模式构建参数
            var exportParams = {
              'mode': exportMode,
              'format': exportFormat
            };
            
            // 合并查询参数
            $.extend(exportParams, queryParams);
            
            if(exportMode === 'single') {
              exportParams.month = $('#single-month-date').val();
              if(!exportParams.month) {
                layer.msg('请选择要导出的月份', {icon: 2});
                return;
              }
            } else if(exportMode === 'multi') {
              var monthRange = $('#month-range-date').val().split(' ~ ');
              exportParams.start_month = monthRange[0];
              exportParams.end_month = monthRange[1];
              if(!exportParams.start_month || !exportParams.end_month) {
                layer.msg('请选择开始和结束月份', {icon: 2});
                return;
              }
            }
            
            // 显示加载中状态
            var loadingIndex = layer.msg('正在导出数据...', {
              icon: 16,
              shade: 0.01,
              time: 0 // 不自动关闭
            });
            
            // 调用导出API
            $.ajax({
              url: '/api/export/user-summary',
              type: 'GET',
              data: exportParams,
              dataType: 'json',
              success: function(res) {
                layer.close(loadingIndex);
                
                if (res.code === 0) {
                  // 导出成功，提示下载
                  if(exportMode === 'multi') {
                    layer.msg('数据导出成功，共 ' + res.data.months + ' 个月的数据', {icon: 1});
                  } else {
                    layer.msg('数据导出成功，共 ' + res.data.rows + ' 条记录', {icon: 1});
                  }
                  
                  // 创建下载链接并自动点击
                  var link = document.createElement('a');
                  link.href = res.data.url;
                  link.download = res.data.file_name;
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                  
                  // 关闭导出对话框
                  layer.close(index);
                } else {
                  // 导出失败
                  layer.msg(res.msg || '导出失败', {icon: 2});
                }
              },
              error: function(xhr, status, error) {
                layer.close(loadingIndex);
                layer.msg('导出请求失败: ' + error, {icon: 2});
              }
            });
          });
        }
      });
    }
  };
} else {
  console.error('Layui is required for UserSummary module');
}